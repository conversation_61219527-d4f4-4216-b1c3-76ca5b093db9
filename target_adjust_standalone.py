# target_adjust_standalone.py - 准心坐标调节工具（独立版本）
# 基于MaixPy框架的触摸屏准心坐标调节工具

from maix import image, display, app, time, camera, touchscreen
from maix import uart as maix_uart  # 重命名避免冲突
import cv2
import numpy as np
import os
import threading

# --------------------------- 内嵌UART模块 ---------------------------
class SimpleUART:
    """简化的UART管理器，内嵌版本"""
    
    def __init__(self):
        self.serial = None
        self.rx_buf = ""  # 接收缓冲区
        self.is_initialized = False
        self.auto_refresh = True  # 自动刷新模式
        
        # 线程安全
        self._buffer_lock = threading.Lock()  # 缓冲区锁
    
    def init(self, device="/dev/ttyS0", baudrate=115200, set_as_global=True):
        """初始化UART
        
        Args:
            device (str): UART设备路径
            baudrate (int): 波特率
            set_as_global (bool): 是否设置为全局实例
        """
        try:
            self.serial = maix_uart.UART(device, baudrate)
            self.serial.set_received_callback(self._on_received)
            self.is_initialized = True
            print(f"UART初始化成功 - {device}:{baudrate}")
            return True
        except Exception as e:
            print(f"UART初始化失败: {str(e)}")
            self.is_initialized = False
            return False
    
    def set_frame(self, header="$$", tail="##", enabled=True):
        """设置帧格式（简化版本，仅用于兼容性）"""
        print(f"帧格式设置: {header}...{tail} ({'启用' if enabled else '禁用'})")
    
    def _on_received(self, serial_obj, data: bytes):
        """UART数据接收回调 - 线程安全"""
        try:
            decoded_data = data.decode('utf-8', errors='replace')
            if decoded_data:
                with self._buffer_lock:
                    if self.auto_refresh:
                        # 直接刷新模式：用新数据替换缓冲区
                        self.rx_buf = decoded_data
                    else:
                        # 累积模式：添加数据到缓冲区
                        self.rx_buf += decoded_data
        except Exception as e:
            print(f"接收错误: {str(e)}")
    
    def send(self, data):
        """发送数据"""
        if not self.is_initialized:
            print("UART未初始化")
            return False
        
        try:
            if isinstance(data, bytes):
                data = data.decode('utf-8', errors='replace')
            else:
                data = str(data)
            
            # 添加换行符
            final_data = data + "\r\n"
            result = self.serial.write_str(final_data)
            return result is not None and result >= 0
        except Exception as e:
            print(f"发送失败: {str(e)}")
            return False
    
    def receive(self):
        """接收数据 - 线程安全"""
        with self._buffer_lock:
            if not self.rx_buf:
                return None
            data = self.rx_buf.strip()
            self.rx_buf = ""  # 清空缓冲区
            return data if data else None
    
    def has_data(self):
        """检查是否有数据 - 线程安全"""
        with self._buffer_lock:
            return len(self.rx_buf) > 0
    
    def clear_buffer(self):
        """清空接收缓冲区 - 线程安全"""
        with self._buffer_lock:
            self.rx_buf = ""
    
    def close(self):
        """关闭UART"""
        if self.serial:
            try:
                self.serial.close()
                print("UART已关闭")
            except:
                pass
        self.is_initialized = False
        self.serial = None

# 激光控制参数
LASER_CONTROL_ENABLED = True     # 激光控制功能使能
SEND_ENABLED = False             # 数据包发送使能标志

# 全局串口对象
uart_instance = None

# --------------------------- 激光控制函数 ---------------------------
def send_laser_control_packet(identifier, current_diff_x=0, current_diff_y=0):
    """发送激光控制数据包

    数据包格式：0x78, 标识符, x高八位, x低八位, y高八位, y低八位, 0xFC

    Args:
        identifier: 激光控制标识符(0x15开启, 0x25关闭)
        current_diff_x, current_diff_y: 当前误差值(可选)
    """
    global uart_instance

    # 检查发送使能标志
    if not SEND_ENABLED or not LASER_CONTROL_ENABLED:
        return False

    try:
        # 取绝对值并限制在16位范围内
        abs_x = min(abs(current_diff_x), 65535)
        abs_y = min(abs(current_diff_y), 65535)

        # 分解为高低八位
        x_high = (abs_x >> 8) & 0xFF
        x_low = abs_x & 0xFF
        y_high = (abs_y >> 8) & 0xFF
        y_low = abs_y & 0xFF

        # 构建数据包
        packet = bytes([0x78, identifier, x_high, x_low, y_high, y_low, 0xFC])

        # 发送二进制数据包
        if uart_instance and hasattr(uart_instance, 'serial') and uart_instance.serial:
            uart_instance.serial.write(packet)
            action = "开启激光" if identifier == 0x15 else "关闭激光"
            print(f"📡 发送激光控制指令: {action} (0x{identifier:02X})")
            return True
        else:
            print("❌ 串口未初始化，无法发送激光控制指令")
            return False

    except Exception as e:
        print(f"❌ 发送激光控制指令失败: {e}")
        return False

def process_uart_command():
    """处理串口接收到的hex指令

    指令说明：
    - 0xFF: 停止串口发送
    - 0x00: 开始发送指令
    """
    global SEND_ENABLED, uart_instance

    try:
        if uart_instance and uart_instance.is_initialized:
            # 使用receive方法获取数据
            received_data = uart_instance.receive()
            if received_data:
                # 如果收到的是乱码字符'�'，说明是0xFF被错误解码了
                if received_data == '�' or received_data == '\ufffd':
                    SEND_ENABLED = False
                    print("📡 收到0xFF指令，停止发送")
                    return

                # 处理字符串数据
                if isinstance(received_data, str):
                    # 检查每个字符的Unicode码点
                    for char in received_data:
                        unicode_val = ord(char)

                        # 检查是否是替换字符（表示原始0xFF）
                        if unicode_val == 0xFFFD:  # Unicode替换字符
                            SEND_ENABLED = False
                            print("📡 收到0xFF指令，停止发送")
                        elif unicode_val == 0x00:
                            SEND_ENABLED = True
                            print("📡 收到0x00指令，开始发送")
                        # 如果字符在ASCII范围内，检查其值
                        elif unicode_val < 256:
                            if unicode_val == 0xFF:
                                SEND_ENABLED = False
                                print("📡 收到0xFF指令，停止发送")
                            elif unicode_val == 0x00:
                                SEND_ENABLED = True
                                print("📡 收到0x00指令，开始发送")

                # 处理字节数据
                elif isinstance(received_data, (bytes, bytearray)):
                    for byte_val in received_data:
                        if byte_val == 0xFF:
                            SEND_ENABLED = False
                            print("📡 收到0xFF指令，停止发送")
                        elif byte_val == 0x00:
                            SEND_ENABLED = True
                            print("📡 收到0x00指令，开始发送")
    except Exception as e:
        pass

# --------------------------- 准心坐标调节工具类 ---------------------------
class TargetAdjustTool:
    """准心坐标调节工具类

    提供准心坐标调节功能，支持X、Y坐标独立调节
    支持触摸屏交互和实时预览
    支持配置文件保存和加载
    """
    
    def __init__(self, initial_x=164, initial_y=122):
        """初始化准心坐标调节工具
        
        Args:
            initial_x (int): 初始X坐标，默认164
            initial_y (int): 初始Y坐标，默认122
        """
        self.target_x = initial_x  # 当前X坐标
        self.target_y = initial_y  # 当前Y坐标
        self.step = 1  # 坐标调节步长
        self.min_x = 0  # 最小X坐标
        self.max_x = 320  # 最大X坐标（基于摄像头分辨率）
        self.min_y = 0  # 最小Y坐标
        self.max_y = 240  # 最大Y坐标（基于摄像头分辨率）

        # 触摸相关参数
        self.button_positions = {}  # 存储按钮位置信息
        self.last_touch_time = 0  # 上次触摸时间
        self.touch_debounce = 200  # 防抖时间(ms)

        # 调试和性能监控参数
        self.debug_enabled = True  # 调试模式开关
        self.frame_count = 0  # 帧计数器
        self.last_fps_time = 0  # 上次FPS计算时间
        self.fps_interval = 5000  # FPS报告间隔(ms)
        self.total_coordinate_changes = 0  # 坐标变化总次数

        # 临时消息显示参数
        self.temp_message = None  # 当前临时消息
        self.temp_message_end = 0  # 消息结束时间

        # 激光控制状态
        self.laser_enabled = False  # 激光是否开启
        self.laser_state = "off"    # 激光状态: off, on
        self.laser_toggle_time = 0  # 上次激光切换时间

        print(f"准心坐标调节工具初始化完成，初始坐标: ({self.target_x}, {self.target_y})")
        if self.debug_enabled:
            print("调试模式已启用 - 目标：准心坐标调节")

    def adjust_coordinate(self, axis, delta):
        """调节坐标

        Args:
            axis (str): 坐标轴（'x' 或 'y'）
            delta (int): 坐标变化量（正数增加，负数减少）

        Returns:
            tuple: 调节后的坐标 (x, y)
        """
        if axis == 'x':
            old_x = self.target_x
            self.target_x = max(self.min_x, min(self.max_x, self.target_x + delta))

            if old_x != self.target_x:
                self.total_coordinate_changes += 1
                action = "增加" if delta > 0 else "减少"
                print(f"X坐标调节: {old_x} -> {self.target_x} ({action})")
                if self.debug_enabled:
                    print(f"累计调节次数: {self.total_coordinate_changes}")

        elif axis == 'y':
            old_y = self.target_y
            self.target_y = max(self.min_y, min(self.max_y, self.target_y + delta))

            if old_y != self.target_y:
                self.total_coordinate_changes += 1
                action = "增加" if delta > 0 else "减少"
                print(f"Y坐标调节: {old_y} -> {self.target_y} ({action})")
                if self.debug_enabled:
                    print(f"累计调节次数: {self.total_coordinate_changes}")

        return (self.target_x, self.target_y)

    def get_coordinates(self):
        """获取当前坐标

        Returns:
            tuple: 当前坐标 (x, y)
        """
        return (self.target_x, self.target_y)

    def set_coordinates(self, x, y):
        """设置坐标

        Args:
            x (int): 新的X坐标
            y (int): 新的Y坐标

        Returns:
            tuple: 设置后的坐标 (x, y)
        """
        old_x, old_y = self.target_x, self.target_y
        self.target_x = max(self.min_x, min(self.max_x, x))
        self.target_y = max(self.min_y, min(self.max_y, y))

        if old_x != self.target_x or old_y != self.target_y:
            print(f"坐标设置: ({old_x}, {old_y}) -> ({self.target_x}, {self.target_y})")

        return (self.target_x, self.target_y)

    def toggle_laser(self):
        """切换激光开关状态

        Returns:
            bool: 激光是否开启
        """
        current_time = time.ticks_ms()

        # 防抖动检查
        if current_time - self.laser_toggle_time < 500:  # 500ms防抖
            return self.laser_enabled

        self.laser_toggle_time = current_time

        if self.laser_enabled:
            # 关闭激光
            self.laser_enabled = False
            self.laser_state = "off"
            send_laser_control_packet(0x25)  # 发送关闭指令
            print("🔴 激光已关闭")
            self.show_temp_message("LASER OFF", 1000)
        else:
            # 开启激光
            self.laser_enabled = True
            self.laser_state = "on"
            send_laser_control_packet(0x15)  # 发送开启指令
            print("🟢 激光已开启")
            self.show_temp_message("LASER ON", 1000)

        return self.laser_enabled

    def show_temp_message(self, message, duration_ms):
        """显示临时消息

        Args:
            message (str): 要显示的消息
            duration_ms (int): 显示持续时间（毫秒）
        """
        self.temp_message = message
        self.temp_message_end = time.ticks_ms() + duration_ms

    def draw_ui(self, img, disp_width, disp_height):
        """绘制用户界面

        Args:
            img: OpenCV图像对象
            disp_width (int): 显示宽度
            disp_height (int): 显示高度

        Returns:
            img: 绘制后的图像
        """
        try:
            # 绘制准心
            center_x, center_y = self.target_x, self.target_y

            # 绘制十字准心
            cross_size = 20
            cross_thickness = 2
            cross_color = (0, 255, 0)  # 绿色

            # 水平线
            cv2.line(img, (center_x - cross_size, center_y),
                    (center_x + cross_size, center_y), cross_color, cross_thickness)
            # 垂直线
            cv2.line(img, (center_x, center_y - cross_size),
                    (center_x, center_y + cross_size), cross_color, cross_thickness)

            # 绘制中心点
            cv2.circle(img, (center_x, center_y), 3, (0, 0, 255), -1)  # 红色实心圆

            # 绘制坐标信息
            coord_text = f'Target: ({self.target_x}, {self.target_y})'
            cv2.putText(img, coord_text, (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

            # 绘制激光状态
            laser_text = f'Laser: {"ON" if self.laser_enabled else "OFF"}'
            laser_color = (0, 255, 0) if self.laser_enabled else (0, 0, 255)
            cv2.putText(img, laser_text, (10, 60),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, laser_color, 2)

            # 绘制发送状态
            send_text = f'Send: {"ON" if SEND_ENABLED else "OFF"}'
            send_color = (0, 255, 0) if SEND_ENABLED else (0, 0, 255)
            cv2.putText(img, send_text, (10, 90),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, send_color, 2)

            # 绘制临时消息
            current_time = time.ticks_ms()
            if self.temp_message and current_time < self.temp_message_end:
                msg_x = disp_width // 2 - 50
                msg_y = disp_height // 2
                cv2.putText(img, self.temp_message, (msg_x, msg_y),
                           cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 255, 255), 3)
            else:
                self.temp_message = None

            return img

        except Exception as e:
            print(f"绘制UI错误: {e}")
            return img

    def draw_buttons(self, img, disp_width, disp_height):
        """绘制控制按钮

        Args:
            img: OpenCV图像对象
            disp_width (int): 显示宽度
            disp_height (int): 显示高度

        Returns:
            dict: 按钮位置信息字典
        """
        try:
            # 定义按钮位置和尺寸
            button_height = 40
            button_width = 60
            y_pos = disp_height - 50

            # 按钮位置信息
            button_positions = {}

            # 绘制坐标显示
            coord_text = f'X:{self.target_x} Y:{self.target_y}'
            cv2.putText(img, coord_text, (10, y_pos),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

            # X坐标调节按钮
            # X减少按钮 (X-)
            x_dec_x = disp_width // 2 - 140
            x_dec_y = y_pos - 20
            cv2.rectangle(img, (x_dec_x, x_dec_y),
                         (x_dec_x + button_width, x_dec_y + button_height),
                         (0, 0, 255), 2)  # 红色边框
            cv2.putText(img, "X-", (x_dec_x + 18, x_dec_y + 28),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
            button_positions['x_decrease'] = (x_dec_x, x_dec_y, button_width, button_height)

            # X增加按钮 (X+)
            x_inc_x = disp_width // 2 - 70
            x_inc_y = y_pos - 20
            cv2.rectangle(img, (x_inc_x, x_inc_y),
                         (x_inc_x + button_width, x_inc_y + button_height),
                         (0, 255, 0), 2)  # 绿色边框
            cv2.putText(img, "X+", (x_inc_x + 18, x_inc_y + 28),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
            button_positions['x_increase'] = (x_inc_x, x_inc_y, button_width, button_height)

            # Y坐标调节按钮
            # Y减少按钮 (Y-)
            y_dec_x = disp_width // 2 + 10
            y_dec_y = y_pos - 20
            cv2.rectangle(img, (y_dec_x, y_dec_y),
                         (y_dec_x + button_width, y_dec_y + button_height),
                         (0, 0, 255), 2)  # 红色边框
            cv2.putText(img, "Y-", (y_dec_x + 18, y_dec_y + 28),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
            button_positions['y_decrease'] = (y_dec_x, y_dec_y, button_width, button_height)

            # Y增加按钮 (Y+)
            y_inc_x = disp_width // 2 + 80
            y_inc_y = y_pos - 20
            cv2.rectangle(img, (y_inc_x, y_inc_y),
                         (y_inc_x + button_width, y_inc_y + button_height),
                         (0, 255, 0), 2)  # 绿色边框
            cv2.putText(img, "Y+", (y_inc_x + 18, y_inc_y + 28),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
            button_positions['y_increase'] = (y_inc_x, y_inc_y, button_width, button_height)

            # 功能按钮
            func_button_width = 70
            func_button_height = 40
            func_button_x = disp_width - 80

            # 退出按钮
            exit_x, exit_y = disp_width - 80, 10
            cv2.rectangle(img, (exit_x, exit_y), (exit_x + 70, exit_y + 30), (255, 0, 0), 2)
            cv2.putText(img, "Exit", (exit_x + 15, exit_y + 22),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)
            button_positions['exit'] = (exit_x, exit_y, 70, 30)

            # 保存按钮
            save_y = 50
            cv2.rectangle(img, (func_button_x, save_y),
                         (func_button_x + func_button_width, save_y + func_button_height),
                         (255, 255, 0), 2)  # 黄色边框
            cv2.putText(img, "Save", (func_button_x + 15, save_y + 28),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
            button_positions['save'] = (func_button_x, save_y, func_button_width, func_button_height)

            # 加载按钮
            load_y = 105
            cv2.rectangle(img, (func_button_x, load_y),
                         (func_button_x + func_button_width, load_y + func_button_height),
                         (0, 255, 255), 2)  # 青色边框
            cv2.putText(img, "Load", (func_button_x + 15, load_y + 28),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
            button_positions['load'] = (func_button_x, load_y, func_button_width, func_button_height)

            # 重置按钮
            reset_y = 160
            cv2.rectangle(img, (func_button_x, reset_y),
                         (func_button_x + func_button_width, reset_y + func_button_height),
                         (255, 0, 255), 2)  # 紫色边框
            cv2.putText(img, "Reset", (func_button_x + 10, reset_y + 28),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 255), 2)
            button_positions['reset'] = (func_button_x, reset_y, func_button_width, func_button_height)

            # 激光按钮
            laser_y = 215
            laser_color = (0, 255, 0) if self.laser_enabled else (128, 128, 128)
            cv2.rectangle(img, (func_button_x, laser_y),
                         (func_button_x + func_button_width, laser_y + func_button_height),
                         laser_color, 2)
            cv2.putText(img, "Laser", (func_button_x + 10, laser_y + 28),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, laser_color, 2)
            button_positions['laser'] = (func_button_x, laser_y, func_button_width, func_button_height)

            self.button_positions = button_positions
            return button_positions

        except Exception as e:
            print(f"绘制按钮错误: {e}")
            return {}

    def handle_touch(self, touch_x, touch_y, pressed, current_time):
        """处理触摸输入

        Args:
            touch_x (int): 触摸X坐标
            touch_y (int): 触摸Y坐标
            pressed (bool): 是否按下
            current_time (int): 当前时间戳

        Returns:
            str or None: 触发的动作名称，None表示无动作
        """
        if not pressed:
            return None

        # 防抖动检查
        if current_time - self.last_touch_time < self.touch_debounce:
            return None

        self.last_touch_time = current_time

        # 检查按钮点击
        for button_name, (x, y, w, h) in self.button_positions.items():
            if x <= touch_x <= x + w and y <= touch_y <= y + h:
                print(f"按钮点击: {button_name} at ({touch_x}, {touch_y})")
                return button_name

        return None

    def save_coordinates_to_file(self, filename="target_coordinates.txt"):
        """保存坐标到文件

        Args:
            filename (str): 文件名

        Returns:
            bool: 保存是否成功
        """
        try:
            with open(filename, 'w') as f:
                f.write(f"target_x = {self.target_x}\n")
                f.write(f"target_y = {self.target_y}\n")
            print(f"[成功] 坐标已保存到 {filename}: ({self.target_x}, {self.target_y})")
            self.show_temp_message("SAVED", 1000)
            return True
        except Exception as e:
            print(f"[错误] 保存坐标失败: {e}")
            self.show_temp_message("SAVE FAILED", 1000)
            return False

    def load_coordinates_from_file(self, filename="target_coordinates.txt"):
        """从文件加载坐标

        Args:
            filename (str): 文件名

        Returns:
            bool: 加载是否成功
        """
        try:
            if not os.path.exists(filename):
                print(f"[错误] 文件不存在: {filename}")
                self.show_temp_message("FILE NOT FOUND", 1000)
                return False

            target_x = None
            target_y = None

            with open(filename, 'r') as f:
                lines = f.readlines()

                for line in lines:
                    line = line.strip()
                    if not line:
                        continue

                    if '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()

                        if key == 'target_x':
                            try:
                                x_value = int(value)
                                if self.min_x <= x_value <= self.max_x:
                                    target_x = x_value
                                else:
                                    print(f"[错误] X坐标超出范围 ({self.min_x}-{self.max_x}): {x_value}")
                            except ValueError:
                                print(f"[错误] X坐标格式错误: {value}")

                        elif key == 'target_y':
                            try:
                                y_value = int(value)
                                if self.min_y <= y_value <= self.max_y:
                                    target_y = y_value
                                else:
                                    print(f"[错误] Y坐标超出范围 ({self.min_y}-{self.max_y}): {y_value}")
                            except ValueError:
                                print(f"[错误] Y坐标格式错误: {value}")

            if target_x is not None and target_y is not None:
                old_x, old_y = self.target_x, self.target_y
                self.target_x = target_x
                self.target_y = target_y
                print(f"[成功] 坐标已从 {filename} 加载: ({old_x}, {old_y}) -> ({self.target_x}, {self.target_y})")
                self.show_temp_message("LOADED", 1000)
                return True
            else:
                print(f"[错误] 文件中未找到有效的坐标数据")
                self.show_temp_message("INVALID DATA", 1000)
                return False

        except Exception as e:
            print(f"[错误] 加载坐标失败: {e}")
            self.show_temp_message("LOAD FAILED", 1000)
            return False

    def reset_coordinates(self):
        """重置坐标到默认值

        Returns:
            bool: 重置是否成功
        """
        try:
            self.target_x = 164  # 重置到默认值
            self.target_y = 122  # 重置到默认值
            print("[成功] 坐标已重置到默认值: (164, 122)")
            self.show_temp_message("RESET", 1000)
            return True
        except Exception as e:
            print(f"[错误] 重置坐标失败: {e}")
            self.show_temp_message("RESET FAILED", 1000)
            return False

# --------------------------- 主程序 ---------------------------
if __name__ == "__main__":
    # 设备初始化
    try:
        print("开始初始化准心坐标调节工具...")

        # 初始化显示屏
        disp = display.Display()
        print(f"显示屏初始化成功，尺寸: {disp.width()}x{disp.height()}")

        # 初始化摄像头
        cam = camera.Camera(320, 240, image.Format.FMT_BGR888)
        print("摄像头初始化成功")

        # 初始化触摸屏
        ts = touchscreen.TouchScreen()
        print("触摸屏初始化成功")

        # 初始化串口
        uart_instance = SimpleUART()
        if uart_instance.init("/dev/ttyS0", 115200, set_as_global=True):
            # 禁用帧格式检测，直接处理原始数据
            uart_instance.set_frame("$$", "##", False)
            print("串口初始化成功")
        else:
            print("串口初始化失败")

        # 初始化准心坐标调节工具
        tool = TargetAdjustTool()

        print("准心坐标调节工具初始化完成！")
        print("使用说明：")
        print("- 实时显示准心位置预览")
        print("- 点击 'X-' 按钮减少X坐标")
        print("- 点击 'X+' 按钮增加X坐标")
        print("- 点击 'Y-' 按钮减少Y坐标")
        print("- 点击 'Y+' 按钮增加Y坐标")
        print("- 点击 'Save' 按钮保存当前坐标到文件")
        print("- 点击 'Load' 按钮从文件加载坐标")
        print("- 点击 'Reset' 按钮重置坐标到默认值")
        print("- 点击 'Laser' 按钮切换激光状态")
        print("- 点击 'Exit' 按钮退出程序")

        # 主循环
        while not app.need_exit():
            try:
                # 处理串口指令
                process_uart_command()

                # 获取摄像头图像
                img = cam.read()
                if img is None:
                    continue
                img_cv = image.image2cv(img, ensure_bgr=False, copy=False)

                # 绘制界面
                display_img = tool.draw_ui(img_cv, disp.width(), disp.height())

                # 绘制按钮
                button_positions = tool.draw_buttons(display_img, disp.width(), disp.height())

                # 处理触摸输入
                if ts.available():
                    touch_data = ts.read()
                    if len(touch_data) >= 3:
                        touch_x, touch_y, pressed = touch_data[0], touch_data[1], touch_data[2]
                        current_time = time.ticks_ms()

                        action = tool.handle_touch(touch_x, touch_y, pressed, current_time)

                        if action:
                            if action == "x_decrease":
                                tool.adjust_coordinate('x', -tool.step)
                            elif action == "x_increase":
                                tool.adjust_coordinate('x', tool.step)
                            elif action == "y_decrease":
                                tool.adjust_coordinate('y', -tool.step)
                            elif action == "y_increase":
                                tool.adjust_coordinate('y', tool.step)
                            elif action == "save":
                                tool.save_coordinates_to_file()
                            elif action == "load":
                                tool.load_coordinates_from_file()
                            elif action == "reset":
                                tool.reset_coordinates()
                            elif action == "laser":
                                tool.toggle_laser()
                            elif action == "exit":
                                print("用户请求退出")
                                break

                # 显示图像
                img_show = image.cv2image(display_img, bgr=True, copy=False)
                disp.show(img_show)

            except Exception as e:
                print(f"主循环错误: {e}")
                continue

    except Exception as e:
        print(f"初始化失败: {e}")

    print("准心坐标调节工具已退出")
